#!/bin/bash

echo "========== [1] 检查 kubelet 和 containerd 状态 =========="
systemctl is-active --quiet kubelet && echo "[✔] kubelet 正在运行" || echo "[✘] kubelet 未运行"
systemctl is-active --quiet containerd && echo "[✔] containerd 正在运行" || echo "[✘] containerd 未运行"

echo -e "\n========== [2] 检查 kubelet 配置文件 =========="
CONF="/etc/systemd/system/kubelet.service.d/10-kubeadm.conf"
if [[ -f "$CONF" ]]; then
    echo "[✔] 找到 $CONF"
    grep -q "container-runtime-endpoint=unix:///run/containerd/containerd.sock" $CONF && echo "[✔] container-runtime-endpoint 配置正确" || echo "[✘] 缺少或错误的 container-runtime-endpoint"
    grep -q "cgroup-driver=systemd" $CONF && echo "[✔] cgroup-driver 设置正确" || echo "[✘] cgroup-driver 设置缺失或错误"
else
    echo "[✘] kubelet 配置文件不存在"
fi

echo -e "\n========== [3] 检查 containerd 配置 =========="
CONFD="/etc/containerd/config.toml"
if [[ -f "$CONFD" ]]; then
    echo "[✔] 找到 $CONFD"
    grep -q 'SystemdCgroup = true' "$CONFD" && echo "[✔] SystemdCgroup 设置为 true" || echo "[✘] SystemdCgroup 设置为 false 或缺失"
else
    echo "[✘] containerd 配置文件不存在"
fi

echo -e "\n========== [4] 测试 crictl 是否能连接 containerd =========="
crictl --runtime-endpoint unix:///run/containerd/containerd.sock info >/dev/null 2>&1 && echo "[✔] crictl 可以连接 containerd" || echo "[✘] crictl 无法连接 containerd"

echo -e "\n========== [5] 获取 kubelet 最新错误日志 =========="
journalctl -u kubelet -n 20 --no-pager | grep -E "E[0-9]{4}" || echo "无明显错误日志"

echo -e "\n========== [6] 检查静态 Pod 运行状态（apiserver 等） =========="
crictl --runtime-endpoint unix:///run/containerd/containerd.sock ps -a | grep kube || echo "未检测到控制平面组件"

echo -e "\n========== [7] 推荐动作提示 =========="
echo "如果看到 ✘ 标记，请手动修复配置后执行："
echo "  sudo systemctl daemon-reexec && systemctl daemon-reload && systemctl restart containerd kubelet"
