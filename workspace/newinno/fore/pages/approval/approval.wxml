<view class="container">
  <!-- 顶部标签栏 -->
  <view class="tabs">
    <view
      wx:for="{{tabs}}"
      wx:key="key"
      class="tab-item {{currentTab === item.key ? 'active' : ''}} {{isLoading ? 'disabled' : ''}}"
      bindtap="switchTab"
      data-tab="{{item.key}}"
    >
      <view class="tab-text">{{item.name}}</view>
      <view class="tab-line" wx:if="{{currentTab === item.key}}"></view>
    </view>
  </view>

  <!-- 预约类型标签栏 -->
  <view class="type-tabs">
    <view
      wx:for="{{typeTabs}}"
      wx:key="key"
      class="type-tab-item {{reservationType === item.key ? 'active' : ''}} {{isLoading ? 'disabled' : ''}}"
      bindtap="switchType"
      data-type="{{item.key}}"
    >
      <view class="type-tab-text">{{item.name}}</view>
      <view class="type-tab-line" wx:if="{{reservationType === item.key}}"></view>
    </view>
  </view>

  <!-- 预约列表 -->
  <view class="records-list">
    <view wx:if="{{isLoading && pendingList.length === 0}}" class="loading">
      <view class="loading-icon"></view>
      <text>加载中...</text>
    </view>

    <view wx:elif="{{pendingList.length === 0}}" class="empty-tip">
      暂无记录
    </view>

    <view wx:for="{{pendingList}}" wx:key="id" class="record-item {{item.type}} {{item.status}}">
      <view class="record-header">
        <view class="header-left">
          <text class="type-tag {{item.type}}">{{item.type === 'venue' ? '场地预约' : (item.type === 'device' ? '设备预约' : '打印机预约')}}</text>
          <text class="status-tag {{item.status}}">{{
            item.status === 'pending' ? '待审批' :
            item.status === 'approved' ? '已通过' :
            item.status === 'rejected' ? '已拒绝' :
            item.status === 'returned' ? '已归还' :
            item.status === 'completed' ? '已完成' : '未知状态'
          }}</text>
        </view>
        <text class="time">{{item.created_at}}</text>
      </view>

      <view class="record-content">
        <!-- 场地预约信息 -->
        <block wx:if="{{item.type === 'venue'}}">
          <view class="info-row">
            <text class="label">场地类型：</text>
            <text>{{item.venue_type === 'lecture_hall' ? '讲座厅' :
                   item.venue_type === 'seminar_room' ? '研讨室' :
                   item.venue_type === 'meeting_room' ? '会议室' :
                   item.venue_type === 'innovation_space' ? '创新工坊' :
                   item.venue_type}}</text>
          </view>
          <view class="info-row">
            <text class="label">预约日期：</text>
            <text>{{item.reservation_date}}</text>
          </view>
          <view class="info-row">
            <text class="label">时间段：</text>
            <text>{{item.business_time === 'morning' ? '上午' : (item.business_time === 'afternoon' ? '下午' : '晚上')}}</text>
          </view>
          <view class="info-row">
            <text class="label">用途说明：</text>
            <text>{{item.purpose}}</text>
          </view>
          <view class="info-row">
            <text class="label">所需设备：</text>
            <view class="devices-list">
              <block wx:if="{{item.devices_needed}}">
                <text wx:for="{{item.devices_needed}}" wx:key="*this" wx:for-item="needed" wx:for-index="deviceName" wx:if="{{needed}}">{{deviceName === 'screen' ? '大屏' : deviceName === 'laptop' ? '笔记本' : deviceName === 'mic_handheld' ? '手持麦' : deviceName === 'mic_gooseneck' ? '鹅颈麦' : deviceName === 'projector' ? '投屏器' : deviceName}}</text>
              </block>
              <text wx:else>无</text>
            </view>
          </view>
        </block>

        <!-- 设备预约信息 -->
        <block wx:elif="{{item.type === 'device'}}">
          <view class="info-row">
            <text class="label">设备名称：</text>
            <text>{{item.device_name}}</text>
          </view>
          <view class="info-row">
            <text class="label">借用时间：</text>
            <text>{{item.borrow_time}}</text>
          </view>
          <view class="info-row">
            <text class="label">归还时间：</text>
            <text>{{item.return_time}}</text>
          </view>
          <view class="info-row" wx:if="{{item.reason}}">
            <text class="label">借用原因：</text>
            <text>{{item.reason}}</text>
          </view>
          <!-- 已归还设备的额外信息 -->
          <block wx:if="{{item.status === 'returned'}}">
            <view class="info-row">
              <text class="label">设备状态：</text>
              <text>{{item.device_condition === 'normal' ? '正常' : item.device_condition === 'damaged' ? '故障' : '未知'}}</text>
            </view>
            <view class="info-row" wx:if="{{item.return_note}}">
              <text class="label">归还备注：</text>
              <text>{{item.return_note}}</text>
            </view>
          </block>
        </block>

        <!-- 打印机预约信息 -->
        <block wx:else>
          <view class="info-row">
            <text class="label">打印机：</text>
            <text>{{item.printer_name === 'printer_1' ? '3D打印机1号' :
                   item.printer_name === 'printer_2' ? '3D打印机2号' : '3D打印机3号'}}</text>
          </view>
          <view class="info-row">
            <text class="label">打印时间：</text>
            <text>{{item.print_time}}</text>
          </view>
        </block>

        <view class="info-row">
          <text class="label">申请人：</text>
          <text>{{item.user_name}} ({{item.user_department}})</text>
        </view>
      </view>

      <view class="record-footer">
        <!-- 只在待审批状态显示按钮 -->
        <block wx:if="{{currentTab === 'pending'}}">
          <button
            class="btn approve"
            size="mini"
            bindtap="showApproveConfirm"
            data-id="{{item.id}}"
            data-type="{{item.type}}"
            disabled="{{isLoading}}"
          >通过</button>
          <button
            class="btn reject"
            size="mini"
            bindtap="showRejectConfirm"
            data-id="{{item.id}}"
            data-type="{{item.type}}"
            disabled="{{isLoading}}"
          >拒绝</button>
        </block>
      </view>
    </view>

    <!-- 加载更多提示 -->
    <view wx:if="{{hasMoreData && !isLoading && pendingList.length > 0}}" class="load-more-tip">
      上拉加载更多
    </view>

    <!-- 加载中提示 -->
    <view wx:if="{{isLoading && pendingList.length > 0}}" class="loading-more">
      <view class="loading-icon"></view>
      <view class="loading-text">加载中...</view>
    </view>

    <!-- 没有更多数据提示 -->
    <view wx:if="{{!hasMoreData && pendingList.length > 0}}" class="no-more-data">
      没有更多数据了
    </view>
  </view>
</view>