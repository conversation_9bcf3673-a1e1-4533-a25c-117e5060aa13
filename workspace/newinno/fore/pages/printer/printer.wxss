/* pages/printer/printer.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(180deg, #F0F2F6 0%, #F7F8FA 100%);
  padding: 24rpx;
}

.printer-list {
  margin-bottom: 32rpx;
}

.printer-item {
  background: rgba(255, 255, 255, 0.95);
  padding: 32rpx;
  margin-bottom: 24rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06);
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.printer-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 6rpx;
  height: 100%;
  background: #007AFF;
  opacity: 0.6;
}

.printer-info {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.printer-icon {
  width: 48rpx;
  height: 48rpx;
  opacity: 0.8;
}

.printer-name {
  font-size: 32rpx;
  color: #1A1A1A;
  font-weight: 500;
}

.printer-status {
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 8rpx;
}

.printer-status.available {
  color: #34C759;
  background: rgba(52, 199, 89, 0.1);
}

.printer-status.busy {
  color: #FF3B30;
  background: rgba(255, 59, 48, 0.1);
}

.reserve-btn {
  min-width: 160rpx;
  margin: 0;
  font-size: 28rpx;
  padding: 16rpx 32rpx;
  background: #007AFF;
  color: #fff;
  border-radius: 16rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.reserve-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(120deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transform: translateX(-100%);
  transition: transform 0.5s;
}

.reserve-btn:active {
  transform: scale(0.96);
  opacity: 0.9;
}

.reserve-btn:active::after {
  transform: translateX(100%);
}

/* 表单样式 */
.form-group {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.04);
}

.form-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 2rpx solid #f5f5f5;
}

.form-item:last-child {
  border-bottom: none;
}

.label {
  min-width: 180rpx;
  max-width: 240rpx;
  font-size: 28rpx;
  color: #333;
  white-space: nowrap;
  overflow: visible;
  font-weight: 500;
  padding-right: 30rpx;
}

.picker {
  flex: 1;
  font-size: 28rpx;
  color: #666;
  background: #F7F8FA;
  padding: 18rpx 24rpx;
  border-radius: 8rpx;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.placeholder-text {
  color: #999;
}

.picker-arrow {
  width: 16rpx;
  height: 16rpx;
  border-right: 2rpx solid #999;
  border-bottom: 2rpx solid #999;
  transform: rotate(45deg);
  margin-left: 10rpx;
}

.reservation-form {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, #F0F2F6 0%, #F7F8FA 100%);
  z-index: 100;
  padding: 32rpx;
  box-sizing: border-box;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.form-header {
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin-bottom: 40rpx;
  padding: 16rpx 0;
}

.back-icon {
  width: 48rpx;
  height: 48rpx;
  opacity: 0.6;
  transition: all 0.3s ease;
}

.back-icon:active {
  opacity: 0.8;
  transform: scale(0.9);
}

.form-header text {
  font-size: 34rpx;
  font-weight: 600;
  color: #1A1A1A;
}

/* 提交按钮样式 */
.submit-btn {
  margin-top: 60rpx;
  border-radius: 12rpx;
  font-weight: 500;
  font-size: 32rpx;
  letter-spacing: 2rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 123, 255, 0.2);
  background: #007AFF !important;
  height: 90rpx;
  line-height: 90rpx;
}

.submit-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 10rpx rgba(0, 123, 255, 0.15);
}

/* 输入框容器样式 */
.input-container {
  flex: 1;
  background: #F7F8FA;
  border-radius: 8rpx;
  padding: 0 24rpx;
  display: flex;
  align-items: center;
}

/* 输入框样式 */
input {
  flex: 1;
  font-size: 28rpx;
  color: #666;
  height: 80rpx;
  padding: 0;
  background: transparent;
}