{% extends "base.html" %}

{% block title %}用户管理{% endblock %}

{% block content %}
<div class="container">
    <h2 class="mb-4">用户管理</h2>

    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">批量导入用户</h5>
        </div>
        <div class="card-body">
            <form id="importForm" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="file">选择Excel文件</label>
                    <input type="file" class="form-control-file" id="file" name="file" accept=".xlsx,.xls">
                </div>
                <button type="submit" class="btn btn-primary">导入</button>
                <button type="button" class="btn btn-secondary" onclick="downloadTemplate()">下载模板</button>
            </form>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">用户列表</h5>
        </div>
        <div class="card-body">
            <!-- 添加搜索功能 -->
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="input-group">
                        <input type="text" id="searchInput" class="form-control" placeholder="搜索用户（姓名、用户名、学院）">
                        <div class="input-group-append">
                            <button class="btn btn-outline-secondary" type="button" id="searchButton">
                                <i class="fas fa-search"></i> 搜索
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-between">
                        <div class="btn-group">
                            <button class="btn btn-primary" type="button" id="filterAll">全部</button>
                            <button class="btn btn-outline-primary" type="button" id="filterStudent">学生</button>
                            <button class="btn btn-outline-primary" type="button" id="filterTeacher">教师</button>
                            <button class="btn btn-outline-primary" type="button" id="filterAdmin">管理员</button>
                        </div>
                        <button id="batchDeleteBtn" class="btn btn-danger" disabled>批量删除</button>
                    </div>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAllUsers">
                                    <label class="form-check-label" for="selectAllUsers"></label>
                                </div>
                            </th>
                            <th>用户名</th>
                            <th>姓名</th>
                            <th>学院</th>
                            <th>角色</th>
                            <th>管理员权限</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="userList">
                        <!-- 用户数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>

            <!-- 分页控件 -->
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div class="d-flex align-items-center">
                    <span id="user-count" class="text-muted">共 0 条记录</span>
                </div>
                <div class="d-flex align-items-center">
                    <label for="page-size-select" class="me-2">每页显示:</label>
                    <select id="page-size-select" class="form-select form-select-sm" style="width: 80px;">
                        <option value="20">20</option>
                        <option value="50" selected>50</option>
                        <option value="100">100</option>
                        <option value="200">200</option>
                    </select>
                </div>
                <nav aria-label="用户列表分页">
                    <ul class="pagination pagination-sm mb-0" id="pagination-controls">
                        <li class="page-item disabled">
                            <button class="page-link" id="prev-page">上一页</button>
                        </li>
                        <li class="page-item disabled">
                            <span class="page-link" id="pagination-info">第 1 页 / 共 1 页</span>
                        </li>
                        <li class="page-item disabled">
                            <button class="page-link" id="next-page">下一页</button>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 全局加载状态控制
window.showLoading = function() {
    document.querySelector('.loading-overlay').style.display = 'flex';
};

window.hideLoading = function() {
    document.querySelector('.loading-overlay').style.display = 'none';
};

// 表格加载状态控制
window.setTableLoading = function(tableId, loading) {
    const table = document.getElementById(tableId);
    if (table) {
        if (loading) {
            table.classList.add('table-loading');
        } else {
            table.classList.remove('table-loading');
        }
    }
};

// 按钮加载状态控制
window.setButtonLoading = function(button, loading) {
    if (loading) {
        button.classList.add('loading');
        button.disabled = true;
        button.setAttribute('data-original-text', button.textContent);
        button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 处理中...';
    } else {
        button.classList.remove('loading');
        button.disabled = false;
        button.innerHTML = button.getAttribute('data-original-text') || button.innerHTML.replace('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 处理中...', '');
    }
};

// 全局任务状态管理器
const TaskManager = {
    // 保存任务状态到localStorage
    saveTask: function(taskId, taskType, description) {
        const tasks = this.getAllTasks();
        tasks[taskId] = {
            id: taskId,
            type: taskType,
            description: description,
            status: 'running',
            progress: 0,
            startTime: new Date().getTime(),
            lastUpdated: new Date().getTime()
        };
        localStorage.setItem('importTasks', JSON.stringify(tasks));
        return tasks[taskId];
    },

    // 更新任务状态
    updateTask: function(taskId, status, progress, result) {
        const tasks = this.getAllTasks();
        if (tasks[taskId]) {
            tasks[taskId].status = status || tasks[taskId].status;
            tasks[taskId].progress = progress !== undefined ? progress : tasks[taskId].progress;
            tasks[taskId].result = result || tasks[taskId].result;
            tasks[taskId].lastUpdated = new Date().getTime();
            localStorage.setItem('importTasks', JSON.stringify(tasks));
        }
        return tasks[taskId];
    },

    // 获取所有任务
    getAllTasks: function() {
        const tasksJson = localStorage.getItem('importTasks');
        return tasksJson ? JSON.parse(tasksJson) : {};
    },

    // 获取单个任务
    getTask: function(taskId) {
        const tasks = this.getAllTasks();
        return tasks[taskId];
    },

    // 获取正在运行的任务
    getRunningTasks: function() {
        const tasks = this.getAllTasks();
        const runningTasks = {};
        for (const taskId in tasks) {
            if (tasks[taskId].status === 'running' || tasks[taskId].status === 'pending') {
                // 检查任务是否超过30分钟没有更新，如果是则认为已失败
                const now = new Date().getTime();
                const lastUpdated = tasks[taskId].lastUpdated;
                if (now - lastUpdated > 30 * 60 * 1000) {
                    tasks[taskId].status = 'failed';
                    tasks[taskId].result = '任务超时，可能已失败';
                    localStorage.setItem('importTasks', JSON.stringify(tasks));
                } else {
                    runningTasks[taskId] = tasks[taskId];
                }
            }
        }
        return runningTasks;
    },

    // 清理已完成的任务（保留最近24小时的）
    cleanupTasks: function() {
        const tasks = this.getAllTasks();
        const now = new Date().getTime();
        const oneDayAgo = now - 24 * 60 * 60 * 1000;

        for (const taskId in tasks) {
            const task = tasks[taskId];
            // 如果任务已完成或失败，且超过24小时，则删除
            if ((task.status === 'completed' || task.status === 'failed') &&
                task.lastUpdated < oneDayAgo) {
                delete tasks[taskId];
            }
        }

        localStorage.setItem('importTasks', JSON.stringify(tasks));
    }
};

// 存储所有用户数据
let allUsers = [];
// 当前筛选条件
let currentFilter = 'all';
let currentSearchTerm = '';
// 分页相关
let currentPage = 1;
let pageSize = 50;
let totalPages = 1;
let totalRecords = 0;

// 创建固定在页面底部的任务状态面板
function createTaskStatusPanel() {
    // 检查是否已存在
    if (document.getElementById('taskStatusPanel')) {
        return;
    }

    const panel = document.createElement('div');
    panel.id = 'taskStatusPanel';
    panel.className = 'task-status-panel';
    panel.innerHTML = `
        <div class="task-status-header">
            <h6 class="mb-0">任务状态</h6>
            <div class="task-status-controls">
                <button type="button" class="btn btn-sm btn-outline-secondary task-minimize-btn" onclick="toggleTaskPanel()">
                    <i class="fas fa-minus"></i>
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary ml-2" onclick="hideTaskPanel()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <div class="task-status-body">
            <div id="taskList" class="task-list">
                <!-- 任务列表将在这里动态添加 -->
                <div class="text-center text-muted py-3">没有正在进行的任务</div>
            </div>
        </div>
    `;

    // 添加到body
    document.body.appendChild(panel);

    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
        .disabled-link {
            pointer-events: none;
            color: #6c757d;
            text-decoration: none;
            cursor: not-allowed;
        }
        .task-status-panel {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 350px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            z-index: 1050;
            overflow: hidden;
            transition: all 0.3s ease;
            max-height: 400px;
            display: flex;
            flex-direction: column;
        }
        .task-status-panel.minimized {
            max-height: 40px;
        }
        .task-status-header {
            padding: 10px 15px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .task-status-body {
            padding: 15px;
            overflow-y: auto;
            flex-grow: 1;
        }
        .task-item {
            margin-bottom: 15px;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
        }
        .task-item:last-child {
            margin-bottom: 0;
        }
        .task-title {
            font-weight: 500;
            margin-bottom: 5px;
        }
        .task-progress {
            height: 8px;
            margin-bottom: 5px;
        }
        .task-info {
            display: flex;
            justify-content: space-between;
            font-size: 0.8rem;
            color: #6c757d;
        }
        .task-status-controls {
            display: flex;
        }
        .task-minimize-btn i {
            transition: transform 0.3s ease;
        }
        .minimized .task-minimize-btn i {
            transform: rotate(180deg);
        }
        .task-hidden {
            display: none;
        }
    `;
    document.head.appendChild(style);
}

// 添加进度条和状态显示区域
function createImportStatusUI() {
    // 检查是否已存在
    if (document.getElementById('importStatus')) {
        return;
    }

    const statusDiv = document.createElement('div');
    statusDiv.id = 'importStatus';
    statusDiv.className = 'mt-3 d-none';
    statusDiv.innerHTML = `
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">导入进度</h6>
                <button type="button" class="btn-close" aria-label="Close" onclick="hideImportStatus()"></button>
            </div>
            <div class="card-body">
                <div class="progress mb-3">
                    <div id="importProgressBar" class="progress-bar progress-bar-striped progress-bar-animated"
                         role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
                <div id="importStatusText" class="text-center">准备导入...</div>
                <div id="importResult" class="mt-3 d-none">
                    <h6>导入结果</h6>
                    <div id="importResultContent"></div>
                </div>
            </div>
        </div>
    `;

    // 添加到表单后面
    const importForm = document.getElementById('importForm');
    importForm.parentNode.insertBefore(statusDiv, importForm.nextSibling);
}

// 显示导入状态UI
function showImportStatus() {
    const statusDiv = document.getElementById('importStatus');
    if (statusDiv) {
        statusDiv.classList.remove('d-none');
    }
}

// 隐藏导入状态UI
function hideImportStatus() {
    const statusDiv = document.getElementById('importStatus');
    if (statusDiv) {
        statusDiv.classList.add('d-none');
        // 重置进度条和状态
        document.getElementById('importProgressBar').style.width = '0%';
        document.getElementById('importProgressBar').setAttribute('aria-valuenow', '0');
        document.getElementById('importStatusText').textContent = '准备导入...';
        document.getElementById('importResult').classList.add('d-none');
    }
}

// 更新进度条
function updateImportProgress(progress, statusText) {
    const progressBar = document.getElementById('importProgressBar');
    const statusTextElement = document.getElementById('importStatusText');

    if (progressBar && statusTextElement) {
        progressBar.style.width = `${progress}%`;
        progressBar.setAttribute('aria-valuenow', progress);
        statusTextElement.textContent = statusText || `导入中 ${progress}%`;
    }
}

// 显示导入结果
function showImportResult(success, message) {
    const resultDiv = document.getElementById('importResult');
    const resultContent = document.getElementById('importResultContent');

    if (resultDiv && resultContent) {
        resultDiv.classList.remove('d-none');
        resultContent.innerHTML = message.replace(/\n/g, '<br>');

        // 设置进度条状态
        const progressBar = document.getElementById('importProgressBar');
        if (progressBar) {
            progressBar.classList.remove('progress-bar-animated');
            if (success) {
                progressBar.classList.remove('bg-danger');
                progressBar.classList.add('bg-success');
            } else {
                progressBar.classList.remove('bg-success');
                progressBar.classList.add('bg-danger');
            }
        }
    }
}

// 更新任务面板中的任务列表
function updateTaskPanel() {
    const taskList = document.getElementById('taskList');
    if (!taskList) return;

    const runningTasks = TaskManager.getRunningTasks();
    const taskIds = Object.keys(runningTasks);

    if (taskIds.length === 0) {
        taskList.innerHTML = '<div class="text-center text-muted py-3">没有正在进行的任务</div>';
        return;
    }

    let html = '';
    for (const taskId of taskIds) {
        const task = runningTasks[taskId];
        const progress = task.progress || 0;
        const statusText = getStatusText({status: task.status, progress: progress});

        html += `
            <div class="task-item" data-task-id="${taskId}">
                <div class="task-title">${task.description || '用户导入任务'}</div>
                <div class="progress task-progress">
                    <div class="progress-bar progress-bar-striped progress-bar-animated"
                         role="progressbar" style="width: ${progress}%"
                         aria-valuenow="${progress}" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
                <div class="task-info">
                    <span>${statusText}</span>
                    <span>开始于: ${new Date(task.startTime).toLocaleTimeString()}</span>
                </div>
            </div>
        `;
    }

    taskList.innerHTML = html;

    // 显示任务面板
    showTaskPanel();
}

// 显示任务面板
function showTaskPanel() {
    createTaskStatusPanel();
    const panel = document.getElementById('taskStatusPanel');
    if (panel) {
        panel.classList.remove('task-hidden');
    }
}

// 隐藏任务面板
function hideTaskPanel() {
    const panel = document.getElementById('taskStatusPanel');
    if (panel) {
        panel.classList.add('task-hidden');
    }
}

// 切换任务面板最小化状态
function toggleTaskPanel() {
    const panel = document.getElementById('taskStatusPanel');
    if (panel) {
        panel.classList.toggle('minimized');
    }
}

// 轮询任务状态
async function pollTaskStatus(taskId) {
    try {
        const response = await fetch(`/api/admin/tasks/${taskId}`);
        if (!response.ok) {
            throw new Error(`获取任务状态失败: ${response.status}`);
        }

        const task = await response.json();
        console.log('Task status:', task);

        // 更新本地任务状态
        TaskManager.updateTask(
            taskId,
            task.status,
            task.progress,
            task.result
        );

        // 更新任务面板
        updateTaskPanel();

        // 更新当前页面的进度条（如果存在）
        updateImportProgress(task.progress, getStatusText(task));

        // 检查任务是否完成
        if (task.status === 'completed') {
            // 显示成功结果
            let message = `成功导入 ${task.result.count} 个用户`;

            // 显示跳过的用户数量
            if (task.result.skipped && task.result.skipped > 0) {
                message += `\n跳过 ${task.result.skipped} 个已存在的用户`;
            }

            // 显示错误信息
            if (task.result.errors && task.result.errors.length > 0) {
                message += `\n失败/跳过详情 ${task.result.errors.length} 条`;
                const errorMessages = task.result.errors.slice(0, 5);
                message += '\n' + errorMessages.join('\n');
                if (task.result.errors.length > 5) {
                    message += `\n... 等共 ${task.result.errors.length} 条信息`;
                }
            }

            showImportResult(true, message);
            loadUsers();  // 重新加载用户列表
            return true;
        } else if (task.status === 'failed') {
            // 显示失败结果
            showImportResult(false, `导入失败: ${task.error || '未知错误'}`);
            return true;
        }

        // 任务仍在进行中，继续轮询
        return false;
    } catch (error) {
        console.error('轮询任务状态失败:', error);
        updateImportProgress(0, '获取任务状态失败');
        showImportResult(false, `获取任务状态失败: ${error.message}`);

        // 更新本地任务状态为失败
        TaskManager.updateTask(taskId, 'failed', 0, {
            error: `获取任务状态失败: ${error.message}`
        });

        // 更新任务面板
        updateTaskPanel();

        return true;  // 停止轮询
    }
}

// 根据任务状态获取显示文本
function getStatusText(task) {
    switch (task.status) {
        case 'pending':
            return '等待处理...';
        case 'running':
            return `导入中 ${task.progress}%`;
        case 'completed':
            return '导入完成';
        case 'failed':
            return '导入失败';
        default:
            return `未知状态: ${task.status}`;
    }
}

// 检查并恢复正在运行的任务
function checkRunningTasks() {
    // 清理过期任务
    TaskManager.cleanupTasks();

    // 获取正在运行的任务
    const runningTasks = TaskManager.getRunningTasks();
    const taskIds = Object.keys(runningTasks);

    if (taskIds.length > 0) {
        console.log(`发现 ${taskIds.length} 个正在运行的任务`);

        // 更新任务面板
        updateTaskPanel();

        // 为每个任务启动轮询
        for (const taskId of taskIds) {
            console.log(`恢复轮询任务: ${taskId}`);

            // 启动轮询
            const pollInterval = setInterval(async () => {
                const isDone = await pollTaskStatus(taskId);
                if (isDone) {
                    clearInterval(pollInterval);
                }
            }, 2000);  // 每2秒轮询一次
        }
    }
}

document.getElementById('importForm').onsubmit = async function(e) {
    e.preventDefault();

    const submitButton = this.querySelector('button[type="submit"]');
    setButtonLoading(submitButton, true);

    const formData = new FormData();
    const fileInput = document.getElementById('file');

    if (!fileInput.files[0]) {
        alert('请选择要导入的文件');
        setButtonLoading(submitButton, false);
        return;
    }

    formData.append('file', fileInput.files[0]);

    // 创建并显示进度UI
    createImportStatusUI();
    showImportStatus();
    updateImportProgress(0, '准备导入...');

    try {
        const response = await fetch('/api/admin/users/import', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();
        if (response.ok) {
            if (result.task_id) {
                // 后台任务模式
                updateImportProgress(5, '文件已上传，开始处理...');

                // 保存任务到本地存储
                const fileName = fileInput.files[0].name;
                TaskManager.saveTask(
                    result.task_id,
                    'user_import',
                    `导入用户 - ${fileName}`
                );

                // 更新任务面板
                updateTaskPanel();

                // 开始轮询任务状态
                const pollInterval = setInterval(async () => {
                    const isDone = await pollTaskStatus(result.task_id);
                    if (isDone) {
                        clearInterval(pollInterval);
                        setButtonLoading(submitButton, false);
                    }
                }, 2000);  // 每2秒轮询一次
            } else {
                // 兼容旧版API响应
                let message = `成功导入 ${result.count || 0} 个用户`;

                // 显示跳过的用户数量
                if (result.skipped && result.skipped > 0) {
                    message += `\n跳过 ${result.skipped} 个已存在的用户`;
                }

                // 显示错误信息
                if (result.errors && result.errors.length > 0) {
                    message += `\n失败/跳过详情 ${result.errors.length} 条`;
                    const errorMessages = result.errors.slice(0, 5);
                    message += '\n' + errorMessages.join('\n');
                    if (result.errors.length > 5) {
                        message += `\n... 等共 ${result.errors.length} 条信息`;
                    }
                }

                updateImportProgress(100, '导入完成');
                showImportResult(true, message);
                loadUsers();  // 重新加载用户列表
                setButtonLoading(submitButton, false);
            }
        } else {
            updateImportProgress(100, '导入失败');
            showImportResult(false, result.error || '导入失败');
            setButtonLoading(submitButton, false);
        }
    } catch (error) {
        console.error('导入失败：', error);
        updateImportProgress(0, '导入失败');
        showImportResult(false, '导入失败：' + (error.message || '未知错误'));
        setButtonLoading(submitButton, false);
    } finally {
        fileInput.value = '';
    }
};

async function loadUsers() {
    console.log(`开始加载用户列表，当前页: ${currentPage}, 每页数量: ${pageSize}`);
    setTableLoading('userList', true);
    try {
        // 构建查询参数
        const params = new URLSearchParams({
            page: currentPage,
            page_size: pageSize
        });

        // 添加搜索条件
        if (currentSearchTerm) {
            params.append('search', currentSearchTerm);
        }

        // 添加角色筛选
        if (currentFilter !== 'all') {
            params.append('role', currentFilter);
        }

        const url = `/api/admin/users?${params.toString()}`;
        console.log(`请求URL: ${url}`);

        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
            }
        });

        console.log(`响应状态: ${response.status}`);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        // 解析响应数据
        const result = await response.json();
        console.log('响应数据:', result);

        // 更新分页信息
        if (result.data) {
            // 新API格式
            allUsers = result.data;
            totalRecords = result.total || 0;
            totalPages = result.total_pages || 1;
            currentPage = result.page || 1;
            console.log(`分页信息更新: 总记录数=${totalRecords}, 总页数=${totalPages}, 当前页=${currentPage}`);
        } else {
            // 兼容旧API格式
            allUsers = result;
            totalRecords = result.length;
            totalPages = 1;
            currentPage = 1;
            console.log(`使用旧API格式: 总记录数=${totalRecords}`);
        }

        // 更新分页控件
        updatePaginationControls();

        // 显示用户数据
        displayUsers(allUsers);
        console.log(`用户列表加载完成，显示了${allUsers.length}条记录`);
    } catch (error) {
        console.error('加载用户列表失败：', error);
        alert('加载用户列表失败：' + error.message);
    } finally {
        setTableLoading('userList', false);
    }
}

// 分页函数
function prevPage() {
    console.log(`尝试切换到上一页，当前页: ${currentPage}, 总页数: ${totalPages}`);
    if (currentPage > 1) {
        currentPage--;
        console.log(`切换到上一页成功，新页码: ${currentPage}`);
        loadUsers();
    } else {
        console.log('已经是第一页，无法切换到上一页');
    }
}

function nextPage() {
    console.log(`尝试切换到下一页，当前页: ${currentPage}, 总页数: ${totalPages}`);
    if (currentPage < totalPages) {
        currentPage++;
        console.log(`切换到下一页成功，新页码: ${currentPage}`);
        loadUsers();
    } else {
        console.log('已经是最后一页，无法切换到下一页');
    }
}

// 更新分页控件
function updatePaginationControls() {
    console.log(`更新分页控件: 当前页=${currentPage}, 总页数=${totalPages}, 总记录数=${totalRecords}`);

    // 更新记录总数
    document.getElementById('user-count').textContent = `共 ${totalRecords} 条记录`;

    // 更新分页信息
    document.getElementById('pagination-info').textContent = `第 ${currentPage} 页 / 共 ${totalPages} 页`;

    // 更新上一页按钮状态
    const prevPageBtn = document.getElementById('prev-page');
    const prevPageItem = prevPageBtn.parentElement;

    if (currentPage <= 1) {
        prevPageBtn.disabled = true;
        prevPageItem.classList.add('disabled');
        console.log('禁用上一页按钮');
    } else {
        prevPageBtn.disabled = false;
        prevPageItem.classList.remove('disabled');
        console.log('启用上一页按钮');
    }

    // 更新下一页按钮状态
    const nextPageBtn = document.getElementById('next-page');
    const nextPageItem = nextPageBtn.parentElement;

    if (currentPage >= totalPages) {
        nextPageBtn.disabled = true;
        nextPageItem.classList.add('disabled');
        console.log('禁用下一页按钮');
    } else {
        nextPageBtn.disabled = false;
        nextPageItem.classList.remove('disabled');
        console.log('启用下一页按钮');
    }
}

function filterAndDisplayUsers() {
    // 重置到第一页
    currentPage = 1;

    // 重新加载用户数据
    loadUsers();
}

function displayUsers(users) {
    const tbody = document.getElementById('userList');
    tbody.innerHTML = users.map(user => {
        const isAdmin = user.role === 'admin';
        return `
            <tr>
                <td>
                    <div class="form-check">
                        <input class="form-check-input user-checkbox" type="checkbox" id="selectUser${user.username}" data-username="${user.username}">
                        <label class="form-check-label" for="selectUser${user.username}"></label>
                    </div>
                </td>
                <td>${user.username || ''}</td>
                <td>${user.name || ''}</td>
                <td>${user.department || ''}</td>
                <td>${getRoleText(user.role) || ''}</td>
                <td>
                    <button
                        class="btn btn-sm ${isAdmin ? 'btn-success' : 'btn-outline-secondary'}"
                        onclick="toggleAdminRole('${user.username}', ${isAdmin})"
                        data-original-text="${isAdmin ? '取消管理员' : '设为管理员'}"
                    >
                        ${isAdmin ? '取消管理员' : '设为管理员'}
                    </button>
                </td>
                <td>
                    <button class="btn btn-sm btn-danger" onclick="deleteUser('${user.username}')">删除</button>
                </td>
            </tr>
        `;
    }).join('');

    // 添加复选框变化事件监听
    document.querySelectorAll('#userList .user-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updateBatchDeleteButton);
    });
}

function getRoleText(role) {
    const roleMap = {
        'student': '学生',
        'teacher': '教师',
        'admin': '管理员'
    };
    return roleMap[role] || role;
}

async function toggleAdminRole(username, isCurrentlyAdmin) {
    if (!confirm(`确定要${isCurrentlyAdmin ? '取消' : '设置'}该用户的管理员权限吗？`)) {
        return;
    }

    try {
        const button = event.target;
        setButtonLoading(button, true);

        const response = await fetch(`/api/admin/users/${username}/toggle-admin`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || '操作失败');
        }

        const result = await response.json();
        alert(result.message || '操作成功');

        // 重新加载用户列表
        loadUsers();
    } catch (error) {
        console.error('修改管理员权限失败:', error);
        alert('修改管理员权限失败: ' + error.message);
    }
}

async function deleteUser(username) {
    if (!confirm('确定要删除此用户吗？')) return;

    const button = event.target;
    setButtonLoading(button, true);

    try {
        const response = await fetch(`/api/admin/users/${username}`, {
            method: 'DELETE'
        });

        if (response.ok) {
            await loadUsers();  // 重新加载用户列表
        } else {
            const error = await response.json();
            alert(error.error || '删除失败');
        }
    } catch (error) {
        alert('删除失败：' + error);
    } finally {
        setButtonLoading(button, false);
    }
}

// 添加下载模板的函数
async function downloadTemplate() {
    try {
        const response = await fetch('/api/admin/templates/user-import', {
            method: 'GET',
            headers: {
                'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            }
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`下载失败: ${errorText}`);
        }

        // 获取文件名
        const contentDisposition = response.headers.get('Content-Disposition');
        let filename = '用户导入模板.xlsx';
        if (contentDisposition) {
            const matches = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/.exec(contentDisposition);
            if (matches != null && matches[1]) {
                filename = matches[1].replace(/['"]/g, '');
            }
        }

        // 下载文件
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        a.remove();

        console.log('Template downloaded successfully');  // 添加调试信息
    } catch (error) {
        console.error('下载模板失败：', error);
        alert('下载模板失败：' + error.message);
    }
}

// 页面加载时获取用户列表
document.addEventListener('DOMContentLoaded', function() {
    // 加载用户列表
    loadUsers();

    // 检查是否有正在运行的任务
    checkRunningTasks();

    // 搜索功能
    document.getElementById('searchButton').addEventListener('click', function() {
        currentSearchTerm = document.getElementById('searchInput').value.trim();
        filterAndDisplayUsers();
    });

    // 回车键搜索
    document.getElementById('searchInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            currentSearchTerm = this.value.trim();
            filterAndDisplayUsers();
        }
    });

    // 全选/取消全选功能
    document.getElementById('selectAllUsers').addEventListener('change', function() {
        const isChecked = this.checked;
        const checkboxes = document.querySelectorAll('#userList input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = isChecked;
        });
        updateBatchDeleteButton();
    });

    // 批量删除按钮事件
    document.getElementById('batchDeleteBtn').addEventListener('click', batchDeleteUsers);

    // 角色筛选功能
    document.getElementById('filterAll').addEventListener('click', function() {
        setActiveFilter(this);
        currentFilter = 'all';
        filterAndDisplayUsers();
    });

    document.getElementById('filterStudent').addEventListener('click', function() {
        setActiveFilter(this);
        currentFilter = 'student';
        filterAndDisplayUsers();
    });

    document.getElementById('filterTeacher').addEventListener('click', function() {
        setActiveFilter(this);
        currentFilter = 'teacher';
        filterAndDisplayUsers();
    });

    document.getElementById('filterAdmin').addEventListener('click', function() {
        setActiveFilter(this);
        currentFilter = 'admin';
        filterAndDisplayUsers();
    });

    // 每页显示数量变更
    document.getElementById('page-size-select').addEventListener('change', function() {
        pageSize = parseInt(this.value);
        currentPage = 1; // 重置到第一页
        loadUsers();
    });

    // 分页按钮事件监听
    document.getElementById('prev-page').addEventListener('click', function(e) {
        e.preventDefault();
        prevPage();
    });

    document.getElementById('next-page').addEventListener('click', function(e) {
        e.preventDefault();
        nextPage();
    });

    // 定期检查任务状态（每30秒）
    setInterval(function() {
        // 检查是否有新的任务需要显示
        const runningTasks = TaskManager.getRunningTasks();
        if (Object.keys(runningTasks).length > 0) {
            updateTaskPanel();
        }
    }, 30000);
});

// 设置当前激活的筛选按钮
function setActiveFilter(button) {
    // 移除所有按钮的 active 类
    document.querySelectorAll('.btn-group .btn').forEach(btn => {
        btn.classList.remove('btn-primary');
        btn.classList.add('btn-outline-primary');
    });

    // 给当前按钮添加 active 类
    button.classList.remove('btn-outline-primary');
    button.classList.add('btn-primary');
}

// 更新批量删除按钮状态
function updateBatchDeleteButton() {
    const checkboxes = document.querySelectorAll('#userList .user-checkbox:checked');
    const batchDeleteBtn = document.getElementById('batchDeleteBtn');
    batchDeleteBtn.disabled = checkboxes.length === 0;
}

// 批量删除用户
async function batchDeleteUsers() {
    const checkboxes = document.querySelectorAll('#userList .user-checkbox:checked');
    const usernames = Array.from(checkboxes).map(cb => cb.dataset.username);
    
    if (usernames.length === 0) {
        alert('请选择要删除的用户');
        return;
    }
    
    if (!confirm(`确定要删除所选的 ${usernames.length} 个用户吗？此操作不可恢复！`)) {
        return;
    }
    
    const button = document.getElementById('batchDeleteBtn');
    setButtonLoading(button, true);
    
    try {
        // 调用批量删除API
        const response = await fetch('/api/admin/users/batch-delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ usernames })
        });
        
        if (response.ok) {
            const result = await response.json();
            alert(result.message || `成功删除 ${result.deleted_count || usernames.length} 个用户`);
            // 重新加载用户列表
            await loadUsers();
        } else {
            const error = await response.json();
            alert(error.error || '批量删除失败');
        }
    } catch (error) {
        console.error('批量删除失败:', error);
        alert('批量删除失败: ' + error.message);
    } finally {
        setButtonLoading(button, false);
    }
}
</script>
{% endblock %}