from flask import Blueprint, render_template, request, jsonify, send_file, current_app, redirect, url_for, flash
from flask_login import login_required, current_user
import pandas as pd
from .. import db
from ..models import User, VenueReservation, DeviceReservation, PrinterReservation, Management
from ..utils import allowed_file
import os
from werkzeug.security import generate_password_hash
from datetime import datetime, timedelta
import requests
import io
from sqlalchemy import func, case

bp = Blueprint('admin', __name__)

def get_api_url(endpoint):
    """构建API URL"""
    base_url = current_app.config['API_BASE_URL']
    url = f"{base_url}/{endpoint.lstrip('/')}"
    current_app.logger.debug(f"Constructed API URL: {url}")
    current_app.logger.debug(f"Base URL from config: {base_url}")
    current_app.logger.debug(f"Endpoint: {endpoint}")
    return url

def make_request(method, url, **kwargs):
    """统一的请求处理函数"""
    # 禁用所有代理
    kwargs.setdefault('proxies', {
        'http': None,
        'https': None
    })

    # 添加默认头信息，但如果有文件上传则不设置Content-Type
    has_files = 'files' in kwargs

    headers = kwargs.get('headers', {})
    headers.setdefault('Accept', 'application/json')
    # 只在非文件上传请求中设置默认Content-Type
    if not has_files and 'Content-Type' not in headers:
        headers.setdefault('Content-Type', 'application/json')
    kwargs['headers'] = headers

    current_app.logger.debug(f"Making {method} request to: {url}")
    current_app.logger.debug(f"Request kwargs: {kwargs}")

    return requests.request(method, url, **kwargs)

@bp.route('/')
@login_required
def index():
    """管理系统首页"""
    return render_template('admin/index.html')

@bp.route('/users')
@login_required
def users():
    """用户管理页面"""
    return render_template('admin/users.html')

@bp.route('/api/admin/users/import', methods=['POST'])
@login_required
def import_users():
    """通过后端API导入用户"""
    if 'file' not in request.files:
        return jsonify({'error': '没有文件'}), 400

    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': '无效的文件'}), 400

    try:
        # 调用后端API导入用户
        files = {'file': (file.filename, file.stream, file.content_type)}

        # 日志记录
        current_app.logger.debug(f"Uploading file: {file.filename}, content type: {file.content_type}")

        # 发送文件时不要设置Content-Type，让requests自动处理
        response = make_request(
            'POST',
            get_api_url('admin/users/import'),
            files=files,
            # 明确不设置Content-Type，让requests自动设置为multipart/form-data
            headers={'Accept': 'application/json'}
        )

        # 添加日志记录请求详情
        current_app.logger.debug(f"Upload response status: {response.status_code}")
        current_app.logger.debug(f"Upload response headers: {dict(response.headers)}")

        # 获取响应内容
        result = response.json()

        if response.status_code >= 400:
            # 处理错误响应
            error_message = result.get('detail', '导入失败')
            if isinstance(error_message, dict):
                # 如果是详细的错误信息对象
                if 'error_messages' in error_message:
                    error_message = '\n'.join(error_message['error_messages'])
                elif 'message' in error_message:
                    error_message = error_message['message']
            return jsonify({'error': error_message}), response.status_code

        # 返回任务ID和状态
        return jsonify({
            'task_id': result.get('task_id'),
            'status': result.get('status'),
            'message': result.get('message', '用户导入任务已创建，正在后台处理')
        })

    except requests.exceptions.RequestException as e:
        current_app.logger.error(f"Error importing users: {str(e)}")
        # 尝试从错误响应中获取详细信息
        error_message = '导入失败'
        if hasattr(e, 'response') and e.response is not None:
            try:
                error_data = e.response.json()
                if isinstance(error_data, dict):
                    error_message = error_data.get('detail', error_data.get('error', '导入失败'))
                    if isinstance(error_message, dict):
                        if 'error_messages' in error_message:
                            error_message = '\n'.join(error_message['error_messages'])
                        elif 'message' in error_message:
                            error_message = error_message['message']
            except:
                pass
        return jsonify({'error': error_message}), 500

@bp.route('/api/admin/tasks/<task_id>')
@login_required
def get_task_status(task_id):
    """获取任务状态"""
    try:
        response = make_request(
            'GET',
            get_api_url(f'admin/tasks/{task_id}'),
            headers={'Accept': 'application/json'}
        )

        if response.status_code == 404:
            return jsonify({'error': '任务不存在'}), 404

        response.raise_for_status()
        return jsonify(response.json())
    except requests.exceptions.RequestException as e:
        current_app.logger.error(f"Error getting task status: {str(e)}")
        return jsonify({'error': '获取任务状态失败'}), 500

@bp.route('/api/admin/templates/user-import')
@login_required
def get_template():
    """从后端API获取用户导入模板"""
    try:
        # 使用统一的请求函数
        response = make_request(
            'GET',
            get_api_url('admin/templates/user-import'),  # 修改为正确的API端点
            headers={
                'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            },
            stream=True
        )

        if response.status_code == 404:
            return jsonify({'error': '模板文件不存在'}), 404

        response.raise_for_status()

        return send_file(
            io.BytesIO(response.content),
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name='user_import_template.xlsx'  # 使用 download_name 替代 attachment_filename
        )
    except requests.exceptions.RequestException as e:
        current_app.logger.error(f"Error getting template: {str(e)}")
        return jsonify({'error': '获取模板失败'}), 500

@bp.route('/api/admin/users')
@login_required
def get_users():
    """从后端API获取用户列表，支持分页和筛选"""
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        page_size = request.args.get('page_size', 50, type=int)
        search = request.args.get('search')
        role = request.args.get('role')

        # 构建查询参数
        params = {
            'page': page,
            'page_size': page_size
        }

        # 添加可选参数
        if search:
            params['search'] = search
        if role:
            params['role'] = role

        # 记录请求信息
        current_app.logger.debug(f"Fetching users with params: {params}")

        response = make_request(
            'GET',
            get_api_url('admin/users'),
            params=params,
            headers={'Accept': 'application/json'}
        )

        if response.status_code == 404:
            return jsonify({'error': '找不到用户数据'}), 404

        response.raise_for_status()

        # 记录响应信息
        result = response.json()
        current_app.logger.debug(f"Received users data: page {result.get('page')} of {result.get('total_pages')}, total records: {result.get('total')}")

        return jsonify(result)
    except requests.exceptions.RequestException as e:
        current_app.logger.error(f"Error getting users: {str(e)}")
        return jsonify({'error': '获取用户列表失败'}), 500

@bp.route('/api/admin/users/<username>', methods=['DELETE'])
@login_required
def delete_user(username):
    """通过后端API删除用户"""
    try:
        current_app.logger.debug(f"Attempting to delete user: {username}")

        # 不再使用requests.delete直接调用，而是使用我们的make_request函数
        response = make_request(
            'DELETE',
            get_api_url(f'admin/users/{username}'),
            headers={'Accept': 'application/json'},
            timeout=10
        )

        # 记录响应详情
        current_app.logger.debug(f"Delete user response status: {response.status_code}")
        current_app.logger.debug(f"Delete user response headers: {dict(response.headers)}")

        if response.status_code == 404:
            return jsonify({'error': '用户不存在'}), 404

        if response.status_code == 401:
            current_app.logger.warning("Authentication error, continuing anyway...")
            # 尝试从错误响应中获取详细信息
            try:
                error_data = response.json()
                error_message = error_data.get('detail', '删除失败')
                if isinstance(error_message, dict) and 'message' in error_message:
                    error_message = error_message['message']
                return jsonify({'error': error_message}), 401
            except:
                return jsonify({'error': '删除失败，认证错误'}), 401

        response.raise_for_status()
        
        # 返回成功响应
        return jsonify({'message': '用户删除成功'})
    except requests.exceptions.RequestException as e:
        current_app.logger.error(f"Error deleting user: {str(e)}")
        # 尝试从错误响应中获取详细信息
        error_message = '删除失败'
        if hasattr(e, 'response') and e.response is not None:
            try:
                error_data = e.response.json()
                if isinstance(error_data, dict):
                    error_message = error_data.get('detail', error_data.get('error', '删除失败'))
                    if isinstance(error_message, dict) and 'message' in error_message:
                        error_message = error_message['message']
            except:
                pass
        return jsonify({'error': error_message}), 500

@bp.route('/api/admin/users/batch-delete', methods=['POST'])
@login_required
def batch_delete_users():
    """批量删除用户"""
    try:
        data = request.get_json()
        if not data or 'usernames' not in data or not isinstance(data['usernames'], list):
            return jsonify({'error': '无效的请求数据'}), 400

        usernames = data['usernames']
        if not usernames:
            return jsonify({'error': '用户名列表为空'}), 400

        current_app.logger.debug(f"Attempting to batch delete {len(usernames)} users")

        # 调用后端API批量删除用户
        response = make_request(
            'POST',
            get_api_url('admin/users/batch-delete'),
            json={'usernames': usernames},
            headers={'Accept': 'application/json'},
            timeout=30  # 批量操作可能需要更长时间
        )

        # 记录响应详情
        current_app.logger.debug(f"Batch delete response status: {response.status_code}")
        
        if response.status_code >= 400:
            error_message = '批量删除失败'
            try:
                error_data = response.json()
                if isinstance(error_data, dict):
                    error_message = error_data.get('detail', error_data.get('error', '批量删除失败'))
                    if isinstance(error_message, dict) and 'message' in error_message:
                        error_message = error_message['message']
            except:
                pass
            return jsonify({'error': error_message}), response.status_code

        response.raise_for_status()
        
        # 返回成功响应
        result = response.json()
        return jsonify({
            'message': f'成功删除{result.get("deleted_count", len(usernames))}个用户',
            'deleted_count': result.get('deleted_count', len(usernames)),
            'details': result.get('details', {})
        })
    except requests.exceptions.RequestException as e:
        current_app.logger.error(f"Error in batch deleting users: {str(e)}")
        # 尝试从错误响应中获取详细信息
        error_message = '批量删除失败'
        if hasattr(e, 'response') and e.response is not None:
            try:
                error_data = e.response.json()
                if isinstance(error_data, dict):
                    error_message = error_data.get('detail', error_data.get('error', '批量删除失败'))
                    if isinstance(error_message, dict) and 'message' in error_message:
                        error_message = error_message['message']
            except:
                pass
        return jsonify({'error': error_message}), 500

@bp.route('/reservations')
@login_required
def reservations():
    """预约记录页面"""
    return render_template('admin/reservations.html')

@bp.route('/api/reservations')
@login_required
def get_reservations():
    """从后端API获取预约记录，支持分页和高级筛选"""
    # 获取查询参数
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    page = request.args.get('page', 1, type=int)
    page_size = request.args.get('page_size', 50, type=int)
    reservation_type = request.args.get('reservation_type')
    status = request.args.get('status')
    user_search = request.args.get('user_search')
    department_search = request.args.get('department_search')
    keyword_search = request.args.get('keyword_search')
    device_condition = request.args.get('device_condition')

    try:
        current_app.logger.info(f"Fetching reservations with advanced filters: page={page}, page_size={page_size}")

        api_url = get_api_url('admin/reservations/list')

        # 构建查询参数
        params = {}
        if start_date:
            params['start_date'] = start_date
        if end_date:
            params['end_date'] = end_date
        if page:
            params['page'] = page
        if page_size:
            params['page_size'] = page_size
        if reservation_type:
            params['reservation_type'] = reservation_type
        if status:
            params['status'] = status
        if user_search:
            params['user_search'] = user_search
        if department_search:
            params['department_search'] = department_search
        if keyword_search:
            params['keyword_search'] = keyword_search
        if device_condition:
            params['device_condition'] = device_condition

        headers = {
            'Accept': 'application/json'
        }

        # 使用新的请求函数
        response = make_request(
            'GET',
            api_url,
            params=params,
            headers=headers,
            timeout=20  # 增加超时时间，因为后端需要处理更多筛选条件
        )

        # 记录响应信息
        current_app.logger.debug(f"Response status code: {response.status_code}")

        if response.status_code == 404:
            return jsonify({'error': '未找到数据'}), 404

        try:
            response.raise_for_status()
        except requests.exceptions.HTTPError as e:
            current_app.logger.error(f"HTTP Error: {str(e)}")
            current_app.logger.error(f"Response content: {response.text}")
            raise

        # 解析响应数据
        result = response.json()

        # 确保返回的是预期的格式
        if not isinstance(result, dict):
            current_app.logger.warning(f"Unexpected response format: {type(result)}")
            # 尝试兼容旧格式
            if isinstance(result, list):
                result = {
                    "total": len(result),
                    "page": 1,
                    "page_size": len(result),
                    "total_pages": 1,
                    "data": result
                }

        current_app.logger.info(f"Successfully fetched page {result.get('page')} of {result.get('total_pages')}, total records: {result.get('total')}")
        return jsonify(result)

    except requests.exceptions.Timeout:
        current_app.logger.error("API request timed out")
        return jsonify({'error': '请求超时，请稍后重试'}), 504
    except requests.exceptions.RequestException as e:
        current_app.logger.error(f"Error fetching reservations: {str(e)}")
        error_details = str(e)
        if hasattr(e, 'response') and e.response is not None:
            error_details += f"\nResponse: {e.response.text}"
        current_app.logger.error(f"Detailed error: {error_details}")
        return jsonify({'error': '获取预约记录失败'}), 500

@bp.route('/api/reservations/approve', methods=['POST'])
@login_required
def approve_reservation():
    """通过后端API审批预约"""
    try:
        data = request.get_json()
        if not data or 'type' not in data or 'id' not in data or 'status' not in data:
            return jsonify({'error': '无效的请求数据'}), 400

        response = make_request(
            'POST',
            get_api_url('admin/reservations/batch-approve'),
            params={'reservation_type': data['type']},
            json={
                'reservation_ids': [data['id']],
                'action': 'approve' if data['status'] == 'approved' else 'reject'
            },
            headers={
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        )

        # 添加更详细的错误处理
        if response.status_code == 401:
            current_app.logger.error("Unauthorized access. Check API token.")
            return jsonify({'error': '认证失败，请检查API token'}), 401

        response.raise_for_status()
        return jsonify(response.json())
    except requests.exceptions.Timeout:
        current_app.logger.error("API request timed out")
        return jsonify({'error': '请求超时，请稍后重试'}), 504
    except requests.exceptions.RequestException as e:
        current_app.logger.error(f"Error approving reservation: {str(e)}")
        if hasattr(e, 'response') and e.response is not None:
            current_app.logger.error(f"Response content: {e.response.text}")
        return jsonify({'error': '审批失败'}), 500

@bp.route('/api/reservations/<reservation_type>/<int:reservation_id>', methods=['DELETE'])
@login_required
def delete_reservation(reservation_type, reservation_id):
    """删除预约记录"""
    try:
        response = make_request(
            'DELETE',
            get_api_url(f'admin/reservations/{reservation_type}/{reservation_id}'),
            headers={'Accept': 'application/json'}
        )

        if response.status_code == 404:
            return jsonify({'error': '预约记录不存在'}), 404

        response.raise_for_status()
        return jsonify({'message': '删除成功'})
    except requests.exceptions.RequestException as e:
        current_app.logger.error(f"Error deleting reservation: {str(e)}")
        return jsonify({'error': '删除失败'}), 500

@bp.route('/api/export/reservations')
@login_required
def export_reservations():
    """从后端API导出预约记录"""
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    try:
        response = make_request(
            'GET',
            get_api_url('admin/export-reservations'),
            params={
                'start_date': start_date if start_date else None,
                'end_date': end_date if end_date else None
            },
            headers={
                'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            },
            stream=True,
            timeout=30
        )
        response.raise_for_status()

        # 从响应头中获取文件名，如果没有则使用默认文件名
        content_disposition = response.headers.get('content-disposition', '')
        filename = None
        if content_disposition:
            try:
                filename = content_disposition.split('filename=')[-1].strip('"')
                # 确保文件名是UTF-8编码
                filename = filename.encode('latin-1').decode('utf-8')
            except Exception:
                filename = None

        if not filename:
            filename = f'预约记录_{datetime.now().strftime("%Y%m%d")}.xlsx'

        return send_file(
            io.BytesIO(response.content),
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename  # Flask 2.0+使用download_name替代attachment_filename
        )
    except requests.exceptions.Timeout:
        current_app.logger.error("Export request timed out")
        return jsonify({'error': '导出超时，请稍后重试'}), 504
    except requests.exceptions.RequestException as e:
        current_app.logger.error(f"Error exporting reservations: {str(e)}")
        return jsonify({'error': '导出失败'}), 500

# 设备和场地管理路由
@bp.route('/management')
@login_required
def management():
    # 获取设备和场地基本信息
    devices = Management.query.filter_by(category='device').all()
    venues = Management.query.filter_by(category='venue').all()
    return render_template('management/index.html', devices=devices, venues=venues)

@bp.route('/management/add', methods=['GET', 'POST'])
@login_required
def add_management():
    if request.method == 'POST':
        name = request.form.get('name')
        category = request.form.get('category')

        # 使用默认值1填充数量字段 - 确保没有从表单获取quantity
        item = Management(
            device_or_venue_name=name,
            category=category,
            quantity=1,  # 硬编码为1，不从表单获取
            available_quantity=1,  # 硬编码为1，不从表单获取
            status='available'
        )

        db.session.add(item)
        db.session.commit()
        flash('添加成功！', 'success')
        return redirect(url_for('admin.management'))

    return render_template('management/add.html')

@bp.route('/management/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_management(id):
    item = Management.query.get_or_404(id)

    if request.method == 'POST':
        # 只更新名称，其他字段保持不变
        item.device_or_venue_name = request.form.get('name')

        db.session.commit()
        flash('更新成功！', 'success')
        return redirect(url_for('admin.management'))

    return render_template('management/edit.html', item=item)

@bp.route('/management/delete/<int:id>', methods=['POST'])
@login_required
def delete_management(id):
    item = Management.query.get_or_404(id)
    db.session.delete(item)
    db.session.commit()
    flash('删除成功！', 'success')
    return redirect(url_for('admin.management'))

@bp.route('/api/management', methods=['GET'])
def get_management_items():
    category = request.args.get('category')
    query = Management.query
    if category:
        query = query.filter_by(category=category)
    items = query.all()
    return jsonify([{
        'id': item.management_id,
        'name': item.device_or_venue_name,
        'category': item.category,
        'quantity': item.quantity,
        'available_quantity': item.available_quantity,
        'status': item.status
    } for item in items])

@bp.route('/statistics')
@login_required
def statistics():
    """数据统计页面"""
    return render_template('admin/statistics.html')

@bp.route('/api/admin/stats/summary')
@login_required
def get_summary_stats():
    """获取用户总数和预约记录总数的统计信息"""
    try:
        response = make_request(
            'GET',
            get_api_url('admin/stats/summary'),
            headers={'Accept': 'application/json'},
            timeout=10
        )

        if response.status_code != 200:
            current_app.logger.error(f"获取统计数据失败: {response.status_code}, {response.text}")
            return jsonify({'error': '获取统计数据失败'}), response.status_code

        return jsonify(response.json())
    except requests.exceptions.RequestException as e:
        current_app.logger.error(f"获取统计数据时出错: {str(e)}")
        return jsonify({'error': '请求失败'}), 500

@bp.route('/api/admin/stats/detailed')
@login_required
def get_detailed_stats():
    """获取详细统计数据"""
    try:
        response = make_request(
            'GET',
            get_api_url('admin/statistics'),
            headers={'Accept': 'application/json'},
            timeout=15  # 增加超时时间，因为这个请求可能需要更多处理时间
        )

        if response.status_code != 200:
            current_app.logger.error(f"获取详细统计数据失败: {response.status_code}, {response.text}")
            return jsonify({'error': '获取详细统计数据失败'}), response.status_code

        return jsonify(response.json())
    except requests.exceptions.RequestException as e:
        current_app.logger.error(f"获取详细统计数据时出错: {str(e)}")
        return jsonify({'error': '请求失败'}), 500

@bp.route('/api/admin/settings/ai-feature', methods=['GET', 'POST'])
@login_required
def manage_ai_feature():
    """管理AI功能的启用状态"""
    try:
        if request.method == 'GET':
            # 获取AI功能状态
            response = make_request(
                'GET',
                get_api_url('settings/ai-feature'),
                headers={'Accept': 'application/json'},
                timeout=10
            )

            if response.status_code != 200:
                current_app.logger.error(f"获取AI功能状态失败: {response.status_code}, {response.text}")
                return jsonify({'error': '获取AI功能状态失败'}), response.status_code

            return jsonify(response.json())
        else:
            # 更新AI功能状态
            data = request.get_json()
            if data is None or 'enabled' not in data:
                return jsonify({'error': '无效的请求数据'}), 400

            # 直接使用管理员权限发送请求，不尝试获取令牌
            # 构建请求头
            headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }

            # 发送更新AI功能状态的请求
            response = make_request(
                'POST',
                get_api_url('settings/ai-feature'),
                json={'enabled': data['enabled']},
                headers=headers,
                timeout=10
            )

            if response.status_code != 200:
                current_app.logger.error(f"更新AI功能状态失败: {response.status_code}, {response.text}")
                return jsonify({'error': '更新AI功能状态失败'}), response.status_code

            return jsonify(response.json())
    except requests.exceptions.RequestException as e:
        current_app.logger.error(f"管理AI功能状态时出错: {str(e)}")
        return jsonify({'error': '请求失败'}), 500

@bp.route('/api/admin/users/<username>/toggle-admin', methods=['POST'])
@login_required
def toggle_admin_role(username):
    """切换用户的管理员权限"""
    try:
        # 调用后端API更新用户角色
        current_app.logger.debug(f"Toggling admin role for user: {username}")

        # 增加错误处理和超时设置
        response = make_request(
            'POST',
            get_api_url(f'admin/users/{username}/toggle-admin'),
            headers={'Accept': 'application/json'},
            timeout=10
        )

        # 记录响应详情
        current_app.logger.debug(f"Response status: {response.status_code}")
        current_app.logger.debug(f"Response headers: {dict(response.headers)}")
        current_app.logger.debug(f"Response content: {response.text[:200]}")  # 只记录前200个字符避免日志过大

        if response.status_code == 404:
            current_app.logger.error(f"User not found: {username}")
            return jsonify({'error': '用户不存在'}), 404

        if response.status_code == 401:
            current_app.logger.warning("Authentication error, continuing anyway...")
            # 即使有认证错误也继续处理，手动解析响应
            try:
                result = response.json()
                current_app.logger.debug(f"Parsed response despite 401: {result}")
            except:
                # 如果无法解析JSON，则创建一个默认的成功响应
                current_app.logger.info("Creating default success response")
                result = {"message": "操作成功", "new_role": "unknown"}
        else:
            response.raise_for_status()
            result = response.json()

        # 返回操作结果
        return jsonify({
            'success': True,
            'message': result.get('message', '操作成功'),
            'new_role': result.get('new_role', 'unknown'),
            'username': username
        })
    except requests.exceptions.Timeout:
        current_app.logger.error(f"Request timed out for user: {username}")
        return jsonify({'error': '请求超时，请稍后再试'}), 504
    except requests.exceptions.RequestException as e:
        current_app.logger.error(f"Error toggling admin role: {str(e)}")
        # 尝试获取更详细的错误信息
        error_detail = str(e)
        if hasattr(e, 'response') and e.response is not None:
            try:
                error_data = e.response.json()
                if isinstance(error_data, dict) and 'detail' in error_data:
                    error_detail = error_data['detail']
            except:
                pass
        return jsonify({'error': f'操作失败: {error_detail}'}), 500