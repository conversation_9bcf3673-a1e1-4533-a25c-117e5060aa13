FROM python:3.9-slim

WORKDIR /app

# 设置时区为UTC+8（中国时区）
ENV TZ=Asia/Shanghai
ENV DEBIAN_FRONTEND=noninteractive

# 创建时区文件（备用方案，不依赖网络）
RUN echo "Asia/Shanghai" > /etc/timezone

# 安装依赖（添加重试机制）
COPY requirements.txt .
RUN pip install --no-cache-dir --retries 5 -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple || \
    (sleep 5 && pip install --no-cache-dir --retries 5 -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple)

# 复制应用代码
COPY app/ ./app/
COPY runme.py .
COPY admin_system/ ./admin_system/

# 设置环境变量
ENV PYTHONPATH=/app

# 暴露端口
EXPOSE 8001
EXPOSE 5000

# 创建管理员账号并启动服务
CMD ["/bin/bash", "-c", "python runme.py & sleep 10 && cd admin_system && python create_admin.py && cd .. && wait"]
