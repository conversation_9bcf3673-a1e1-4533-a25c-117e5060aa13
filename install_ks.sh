#!/bin/bash

set -e

echo "====== Step 1: 设置 containerd 国内镜像源并配置 cgroup 驱动 ======"

mkdir -p /etc/containerd
containerd config default | sudo tee /etc/containerd/config.toml > /dev/null

# 修改为 SystemdCgroup = true
sed -i 's/SystemdCgroup = false/SystemdCgroup = true/' /etc/containerd/config.toml

# 设置阿里云镜像加速
sed -i '/\[plugins."io.containerd.grpc.v1.cri".registry.mirrors\]/a\
  [plugins."io.containerd.grpc.v1.cri".registry.mirrors."registry.cn-hangzhou.aliyuncs.com"]\n\
    endpoint = ["https://registry.cn-hangzhou.aliyuncs.com"]' /etc/containerd/config.toml

systemctl daemon-reload
systemctl restart containerd
systemctl enable containerd

echo "====== Step 2: 配置 kubelet 使用 containerd ======"

cat > /etc/systemd/system/kubelet.service.d/10-kubeadm.conf <<EOF
[Service]
Environment="KUBELET_KUBECONFIG_ARGS=--bootstrap-kubeconfig=/etc/kubernetes/bootstrap-kubelet.conf"
Environment="KUBELET_CONFIG_ARGS=--config=/var/lib/kubelet/config.yaml"
EnvironmentFile=-/var/lib/kubelet/kubeadm-flags.env
EnvironmentFile=-/etc/default/kubelet
ExecStart=
ExecStart=/usr/bin/kubelet \$KUBELET_KUBECONFIG_ARGS \$KUBELET_CONFIG_ARGS \$KUBELET_KUBEADM_ARGS \$KUBELET_EXTRA_ARGS
Environment="KUBELET_EXTRA_ARGS=--container-runtime=remote --container-runtime-endpoint=unix:///run/containerd/containerd.sock"
EOF

systemctl daemon-reexec
systemctl daemon-reload
systemctl restart kubelet
systemctl enable kubelet

echo "====== Step 3: 拉取 Kubernetes 所需镜像（国内镜像源）======"

# 定义版本（请根据需要替换）
K8S_VERSION="v1.29.0"

# 替换默认镜像仓库地址为阿里云
kubeadm config images pull \
  --kubernetes-version=${K8S_VERSION} \
  --image-repository=registry.cn-hangzhou.aliyuncs.com/google_containers

echo "====== Step 4: 初始化 Kubernetes 集群 ======"

kubeadm init \
  --image-repository=registry.cn-hangzhou.aliyuncs.com/google_containers \
  --kubernetes-version=${K8S_VERSION} \
  --pod-network-cidr=10.244.0.0/16 \
  --ignore-preflight-errors=all -v=5

echo "====== Step 5: 配置 kubectl 权限 ======"

mkdir -p $HOME/.kube
cp -i /etc/kubernetes/admin.conf $HOME/.kube/config
chown $(id -u):$(id -g) $HOME/.kube/config

echo "====== Step 6: 安装 Flannel 网络插件（国内 YAML 镜像源） ======"

kubectl apply -f https://raw.githubusercontent.com/coreos/flannel/master/Documentation/kube-flannel.yml

echo "✅ Kubernetes 安装完成！"
